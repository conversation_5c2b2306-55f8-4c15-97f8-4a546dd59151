<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.yqzl.mapper.CiticTransferOrderMapper">

    <resultMap type="CiticTransferOrder" id="CiticTransferOrderResult">
        <result property="id"    column="id"    />
        <result property="clientId"    column="client_id"    />
        <result property="preFlg"    column="pre_flg"    />
        <result property="preDate"    column="pre_date"    />
        <result property="preTime"    column="pre_time"    />
        <result property="payType"    column="pay_type"    />
        <result property="payFlg"    column="pay_flg"    />
        <result property="payAccountNo"    column="pay_account_no"    />
        <result property="recAccountNo"    column="rec_account_no"    />
        <result property="recAccountName"    column="rec_account_name"    />
        <result property="recOpenBankName"    column="rec_open_bank_name"    />
        <result property="recOpenBankCode"    column="rec_open_bank_code"    />
        <result property="tranAmount"    column="tran_amount"    />
        <result property="abstractField"    column="abstract_field"    />
        <result property="memo"    column="memo"    />
        <result property="chkNum"    column="chk_num"    />
        <result property="handledBy"    column="handled_by"    />
        <result property="reviewer"    column="reviewer"    />
        <result property="state"    column="state"    />
    </resultMap>

    <sql id="selectCiticTransferOrderVo">
        select id, client_id, pre_flg, pre_date, pre_time, pay_type, pay_flg, pay_account_no, rec_account_no, rec_account_name, rec_open_bank_name, rec_open_bank_code, tran_amount, abstract_field, memo, chk_num, handled_by, reviewer, state from yqzl_citic_transfer_order
    </sql>

    <select id="selectCiticTransferOrderList" parameterType="CiticTransferOrder" resultMap="CiticTransferOrderResult">
        <include refid="selectCiticTransferOrderVo"/>
        <where>
            <if test="clientId != null  and clientId != ''"> and client_id = #{clientId}</if>
            <if test="preFlg != null  and preFlg != ''"> and pre_flg = #{preFlg}</if>
            <if test="preDate != null  and preDate != ''"> and pre_date = #{preDate}</if>
            <if test="preTime != null  and preTime != ''"> and pre_time = #{preTime}</if>
            <if test="payType != null  and payType != ''"> and pay_type = #{payType}</if>
            <if test="payFlg != null  and payFlg != ''"> and pay_flg = #{payFlg}</if>
            <if test="payAccountNo != null  and payAccountNo != ''"> and pay_account_no = #{payAccountNo}</if>
            <if test="recAccountNo != null  and recAccountNo != ''"> and rec_account_no = #{recAccountNo}</if>
            <if test="recAccountName != null  and recAccountName != ''"> and rec_account_name like concat('%', #{recAccountName}, '%')</if>
            <if test="recOpenBankName != null  and recOpenBankName != ''"> and rec_open_bank_name like concat('%', #{recOpenBankName}, '%')</if>
            <if test="recOpenBankCode != null  and recOpenBankCode != ''"> and rec_open_bank_code = #{recOpenBankCode}</if>
            <if test="tranAmount != null "> and tran_amount = #{tranAmount}</if>
            <if test="abstractField != null  and abstractField != ''"> and abstract_field = #{abstractField}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="chkNum != null  and chkNum != ''"> and chk_num = #{chkNum}</if>
            <if test="handledBy != null  and handledBy != ''"> and handled_by = #{handledBy}</if>
            <if test="reviewer != null  and reviewer != ''"> and reviewer = #{reviewer}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
        </where>
    </select>

    <select id="selectCiticTransferOrderById" parameterType="Long" resultMap="CiticTransferOrderResult">
        <include refid="selectCiticTransferOrderVo"/>
        where id = #{id}
    </select>

    <insert id="insertCiticTransferOrder" parameterType="CiticTransferOrder" useGeneratedKeys="true" keyProperty="id">
        insert into yqzl_citic_transfer_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clientId != null and clientId != ''">client_id,</if>
            <if test="preFlg != null and preFlg != ''">pre_flg,</if>
            <if test="preDate != null">pre_date,</if>
            <if test="preTime != null">pre_time,</if>
            <if test="payType != null and payType != ''">pay_type,</if>
            <if test="payFlg != null and payFlg != ''">pay_flg,</if>
            <if test="payAccountNo != null and payAccountNo != ''">pay_account_no,</if>
            <if test="recAccountNo != null and recAccountNo != ''">rec_account_no,</if>
            <if test="recAccountName != null and recAccountName != ''">rec_account_name,</if>
            <if test="recOpenBankName != null">rec_open_bank_name,</if>
            <if test="recOpenBankCode != null">rec_open_bank_code,</if>
            <if test="tranAmount != null">tran_amount,</if>
            <if test="abstractField != null and abstractField != ''">abstract_field,</if>
            <if test="memo != null">memo,</if>
            <if test="chkNum != null">chk_num,</if>
            <if test="handledBy != null">handled_by,</if>
            <if test="reviewer != null">reviewer,</if>
            <if test="state != null">state,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clientId != null and clientId != ''">#{clientId},</if>
            <if test="preFlg != null and preFlg != ''">#{preFlg},</if>
            <if test="preDate != null">#{preDate},</if>
            <if test="preTime != null">#{preTime},</if>
            <if test="payType != null and payType != ''">#{payType},</if>
            <if test="payFlg != null and payFlg != ''">#{payFlg},</if>
            <if test="payAccountNo != null and payAccountNo != ''">#{payAccountNo},</if>
            <if test="recAccountNo != null and recAccountNo != ''">#{recAccountNo},</if>
            <if test="recAccountName != null and recAccountName != ''">#{recAccountName},</if>
            <if test="recOpenBankName != null">#{recOpenBankName},</if>
            <if test="recOpenBankCode != null">#{recOpenBankCode},</if>
            <if test="tranAmount != null">#{tranAmount},</if>
            <if test="abstractField != null and abstractField != ''">#{abstractField},</if>
            <if test="memo != null">#{memo},</if>
            <if test="chkNum != null">#{chkNum},</if>
            <if test="handledBy != null">#{handledBy},</if>
            <if test="reviewer != null">#{reviewer},</if>
            <if test="state != null">#{state},</if>
         </trim>
    </insert>

    <update id="updateCiticTransferOrder" parameterType="CiticTransferOrder">
        update yqzl_citic_transfer_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="clientId != null and clientId != ''">client_id = #{clientId},</if>
            <if test="preFlg != null and preFlg != ''">pre_flg = #{preFlg},</if>
            <if test="preDate != null">pre_date = #{preDate},</if>
            <if test="preTime != null">pre_time = #{preTime},</if>
            <if test="payType != null and payType != ''">pay_type = #{payType},</if>
            <if test="payFlg != null and payFlg != ''">pay_flg = #{payFlg},</if>
            <if test="payAccountNo != null and payAccountNo != ''">pay_account_no = #{payAccountNo},</if>
            <if test="recAccountNo != null and recAccountNo != ''">rec_account_no = #{recAccountNo},</if>
            <if test="recAccountName != null and recAccountName != ''">rec_account_name = #{recAccountName},</if>
            <if test="recOpenBankName != null">rec_open_bank_name = #{recOpenBankName},</if>
            <if test="recOpenBankCode != null">rec_open_bank_code = #{recOpenBankCode},</if>
            <if test="tranAmount != null">tran_amount = #{tranAmount},</if>
            <if test="abstractField != null and abstractField != ''">abstract_field = #{abstractField},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="chkNum != null">chk_num = #{chkNum},</if>
            <if test="handledBy != null">handled_by = #{handledBy},</if>
            <if test="reviewer != null">reviewer = #{reviewer},</if>
            <if test="state != null">state = #{state},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCiticTransferOrderById" parameterType="Long">
        delete from yqzl_citic_transfer_order where id = #{id}
    </delete>

    <delete id="deleteCiticTransferOrderByIds" parameterType="String">
        delete from yqzl_citic_transfer_order where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

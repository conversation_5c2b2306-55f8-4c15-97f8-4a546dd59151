package org.ruoyi.core.yqzl.domain;

import java.math.BigDecimal;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 中信银行支付转账对象 yqzl_citic_transfer_order
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
public class CiticTransferOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 客户流水号 */
    @Excel(name = "客户流水号")
    private String clientId;

    /** 预约支付标志 0非预约 1预约 2次日 */
    @Excel(name = "预约支付标志 0非预约 1预约 2次日")
    @NotBlank(message = "支付日期不可为空")
    private String preFlg;

    /** 延期支付日期 YYYYMMDD */
    @Excel(name = "延期支付日期 YYYYMMDD")
    private String preDate;

    /** 延期支付时间 hhmmss */
    @Excel(name = "延期支付时间 hhmmss")
    private String preTime;

    /** 支付方式 1跨行 2行内 3企业内部 */
    @Excel(name = "支付方式 1跨行 2行内 3企业内部")
    @NotBlank(message = "支付方式不可为空")
    private String payType;

    /** 支付时效 0加急 1普通 */
    @Excel(name = "支付时效 0加急 1普通")
    @NotBlank(message = "支付时效不可为空")
    private String payFlg;

    /** 付款账号 */
    @Excel(name = "付款账号")
    @NotBlank(message = "付款账号不可为空")
    private String payAccountNo;

    /** 收款账号 */
    @Excel(name = "收款账号")
    @NotBlank(message = "收方账户不可为空")
    private String recAccountNo;

    /** 收款账号名称 */
    @Excel(name = "收款账号名称")
    @NotBlank(message = "收方户名不可为空")
    private String recAccountName;

    /** 收款开户行名 */
    @Excel(name = "收款开户行名")
//    @NotBlank(message = "收方开户行不可为空")
    private String recOpenBankName;

    /** 收款开户行网点号 */
    @Excel(name = "收款开户行网点号")
    private String recOpenBankCode;

    /** 金额 */
    @Excel(name = "金额")
    @NotNull(message = "金额不可为空")
    private BigDecimal tranAmount;

    /** 附言 */
    @Excel(name = "附言")
    @NotBlank(message = "附言不可为空")
    private String abstractField;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 对账编号 */
    @Excel(name = "对账编号")
    private String chkNum;

    /** 经办人 */
    @Excel(name = "经办人")
    private String handledBy;

    /** 审核人 */
    @Excel(name = "审核人")
    private String reviewer;

    /** 状态 1.未提交、2.审核中、3.审核通过、4.交易中、5.等待交易、6.交易成功、7.交易失败 */
    @Excel(name = "状态 1.未提交、2.审核中、3.审核通过、4.交易中、5.等待交易、6.交易成功、7.交易失败")
    private String state;

}

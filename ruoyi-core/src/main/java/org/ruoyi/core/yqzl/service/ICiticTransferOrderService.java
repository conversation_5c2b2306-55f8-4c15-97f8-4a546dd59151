package org.ruoyi.core.yqzl.service;

import org.ruoyi.core.yqzl.domain.CiticTransferOrder;

import java.util.List;

/**
 * 中信银行支付转账Service接口
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface ICiticTransferOrderService
{
    /**
     * 查询中信银行支付转账
     *
     * @param id 中信银行支付转账主键
     * @return 中信银行支付转账
     */
    public CiticTransferOrder selectCiticTransferOrderById(Long id);

    /**
     * 查询中信银行支付转账列表
     *
     * @param citicTransferOrder 中信银行支付转账
     * @return 中信银行支付转账集合
     */
    public List<CiticTransferOrder> selectCiticTransferOrderList(CiticTransferOrder citicTransferOrder);

    /**
     * 新增中信银行支付转账
     *
     * @param citicTransferOrder 中信银行支付转账
     * @return 结果
     */
    public int insertCiticTransferOrder(CiticTransferOrder citicTransferOrder);

    /**
     * 修改中信银行支付转账
     *
     * @param citicTransferOrder 中信银行支付转账
     * @return 结果
     */
    public int updateCiticTransferOrder(CiticTransferOrder citicTransferOrder);

    /**
     * 批量删除中信银行支付转账
     *
     * @param ids 需要删除的中信银行支付转账主键集合
     * @return 结果
     */
    public int deleteCiticTransferOrderByIds(Long[] ids);

    /**
     * 删除中信银行支付转账信息
     *
     * @param id 中信银行支付转账主键
     * @return 结果
     */
    public int deleteCiticTransferOrderById(Long id);
}

package org.ruoyi.core.yqzl.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.ruoyi.core.yqzl.domain.CiticTransferOrder;
import org.ruoyi.core.yqzl.service.ICiticTransferOrderService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 中信银行支付转账Controller
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/yqzl/transfer/order")
public class CiticTransferOrderController extends BaseController
{
    @Autowired
    private ICiticTransferOrderService citicTransferOrderService;

    /**
     * 查询中信银行支付转账列表
     */
    //@PreAuthorize("@ss.hasPermi('system:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(CiticTransferOrder citicTransferOrder)
    {
        startPage();
        List<CiticTransferOrder> list = citicTransferOrderService.selectCiticTransferOrderList(citicTransferOrder);
        return getDataTable(list);
    }

    /**
     * 导出中信银行支付转账列表
     */
    //@PreAuthorize("@ss.hasPermi('system:order:export')")
    @Log(title = "中信银行支付转账", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,CiticTransferOrder citicTransferOrder)
    {
        List<CiticTransferOrder> list = citicTransferOrderService.selectCiticTransferOrderList(citicTransferOrder);
        ExcelUtil<CiticTransferOrder> util = new ExcelUtil<CiticTransferOrder>(CiticTransferOrder.class);
        util.exportExcel(response, list, "中信银行支付转账数据");
    }

    /**
     * 获取中信银行支付转账详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(citicTransferOrderService.selectCiticTransferOrderById(id));
    }

    /**
     * 新增中信银行支付转账
     */
    //@PreAuthorize("@ss.hasPermi('system:order:add')")
    @Log(title = "中信银行支付转账", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody CiticTransferOrder citicTransferOrder)
    {
        return toAjax(citicTransferOrderService.insertCiticTransferOrder(citicTransferOrder));
    }

    /**
     * 修改中信银行支付转账
     */
    //@PreAuthorize("@ss.hasPermi('system:order:edit')")
    @Log(title = "中信银行支付转账", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CiticTransferOrder citicTransferOrder)
    {
        return toAjax(citicTransferOrderService.updateCiticTransferOrder(citicTransferOrder));
    }

    /**
     * 删除中信银行支付转账
     */
    //@PreAuthorize("@ss.hasPermi('system:order:remove')")
    @Log(title = "中信银行支付转账", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(citicTransferOrderService.deleteCiticTransferOrderByIds(ids));
    }
}
